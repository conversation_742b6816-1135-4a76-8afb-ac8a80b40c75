import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Container,
  Paper,
  Box,
  Chip,
  Alert,
  Snackbar,
  IconButton,
  Fade,
  ThemeProvider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import {
  VideoLibrary,
  Compress,
  PlayArrow,
  AccountCircle,
  Logout,
  Refresh,
  CheckCircle,
  CheckCircleOutline,
  SelectAll,
  Clear
} from '@mui/icons-material';
import * as signalR from "@microsoft/signalr";
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import Subscribe from './Subscribe';
import AuthCallback from './AuthCallback';
import { ThemeProvider as CustomThemeProvider, useTheme } from './contexts/ThemeContext';
import { getTheme } from './theme/themes';
import ThemeSelector from './components/ThemeSelector';
import CompressionStatusIndicator, { CompressionStatus } from './components/CompressionStatusIndicator';

// Main App component that uses the theme context
const AppContent: React.FC = () => {
  const { effectiveTheme } = useTheme();
  const theme = getTheme(effectiveTheme);

interface MediaItem {
  id: string;
  filename: string;
  mimeType: string;
  baseUrl: string;
  mediaMetadata?: {
    width: string;
    height: string;
    creationTime: string;
    video?: {
      fps: number;
      status: string;
    };
  };
}

  const [token, setToken] = useState<string | null>(localStorage.getItem('jwt'));
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [notification, setNotification] = useState<string | null>(null);
  const [notificationType, setNotificationType] = useState<'success' | 'error' | 'info'>('info');
  const [videosLoading, setVideosLoading] = useState(false);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [lastSelectedIndex, setLastSelectedIndex] = useState<number | null>(null);
  const [compressionModalOpen, setCompressionModalOpen] = useState(false);
  const [compressionTarget, setCompressionTarget] = useState<MediaItem | 'batch' | null>(null);
  const [compressionSettings, setCompressionSettings] = useState({
    quality: 'medium',
    uploadToGooglePhotos: true,
    overwriteOriginal: false
  });
  const [compressionStatuses, setCompressionStatuses] = useState<Map<string, CompressionStatus>>(new Map());

  // Helper function for showing notifications
  const showNotification = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setNotification(message);
    setNotificationType(type);
  };

  // Helper function to get current user ID from JWT token
  const getCurrentUserId = () => {
    if (!token) return null;
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.sub || payload.nameid || payload.userId;
    } catch {
      return null;
    }
  };

  // Function to refresh token from localStorage (called by AuthCallback)
  const refreshToken = () => {
    const newToken = localStorage.getItem('jwt');
    setToken(newToken);
  };

  // Selection handling functions
  const handleItemClick = (mediaItem: MediaItem, index: number, event: React.MouseEvent) => {
    // Prevent default browser selection behavior
    event.preventDefault();

    const itemId = mediaItem.id;

    if (event.shiftKey && lastSelectedIndex !== null) {
      // Shift-click: select range
      const start = Math.min(lastSelectedIndex, index);
      const end = Math.max(lastSelectedIndex, index);
      const newSelected = new Set(selectedItems);

      for (let i = start; i <= end; i++) {
        if (mediaItems[i]?.id) {
          newSelected.add(mediaItems[i].id);
        }
      }

      setSelectedItems(newSelected);
    } else if (event.ctrlKey || event.metaKey) {
      // Ctrl/Cmd-click: toggle individual item
      const newSelected = new Set(selectedItems);
      if (newSelected.has(itemId)) {
        newSelected.delete(itemId);
      } else {
        newSelected.add(itemId);
      }
      setSelectedItems(newSelected);
      setLastSelectedIndex(index);
    } else {
      // Regular click: handle selection/deselection
      const newSelected = new Set(selectedItems);

      if (selectedItems.has(itemId) && selectedItems.size === 1) {
        // If this is the only selected item, deselect it
        newSelected.clear();
        setLastSelectedIndex(null);
      } else {
        // Otherwise, select only this item
        newSelected.clear();
        newSelected.add(itemId);
        setLastSelectedIndex(index);
      }

      setSelectedItems(newSelected);
    }
  };

  const handleSelectAll = () => {
    const allIds = mediaItems.map(item => item.id).filter(Boolean);
    setSelectedItems(new Set(allIds));
  };

  const handleClearSelection = () => {
    setSelectedItems(new Set());
    setLastSelectedIndex(null);
  };

  // Make refreshToken available globally for AuthCallback
  useEffect(() => {
    (window as any).refreshToken = refreshToken;
    return () => {
      delete (window as any).refreshToken;
    };
  }, []);

  useEffect(() => {
    if (!token) return;

    // Test backend connectivity first
    const testBackendConnectivity = async () => {
      try {
        const response = await fetch('/api/videos/compression-jobs?page=1&pageSize=1');
        console.log("Backend connectivity test:", response.status);
        return response.ok;
      } catch (err) {
        console.error("Backend connectivity test failed:", err);
        return false;
      }
    };

    // Use absolute URL to bypass proxy issues with WebSockets
    const hubUrl = "http://localhost:5119/notificationHub";

    console.log("SignalR connecting to:", hubUrl);

    // Test backend connectivity
    testBackendConnectivity();

    const connection = new signalR.HubConnectionBuilder()
      .withUrl(hubUrl, {
        accessTokenFactory: () => token || "",
        transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.LongPolling,
        withCredentials: true
      })
      .configureLogging(signalR.LogLevel.Information)
      .build();

    connection.on("ReceiveMessage", (user, message) => {
      console.log("Received message:", user, message);
      setNotification(`${user}: ${message}`);
    });

    connection.on("TestResponse", (message) => {
      console.log("Test response received:", message);
    });

    connection.on("CompressionStatusUpdate", (statusUpdate) => {
      console.log("Received compression status update:", statusUpdate);

      // Only update if this status update is for our user or if it's a broadcast
      if (!statusUpdate.userId || statusUpdate.userId === getCurrentUserId()) {
        setCompressionStatuses(prev => {
          const newMap = new Map(prev);
          newMap.set(statusUpdate.mediaItemId, {
            jobId: statusUpdate.jobId,
            status: statusUpdate.status,
            message: statusUpdate.message,
            progress: statusUpdate.progress,
            error: statusUpdate.error
          });
          return newMap;
        });
      }
    });

    // Add connection state change logging
    connection.onclose((error) => {
      console.log("SignalR connection closed:", error);
    });

    connection.onreconnecting((error) => {
      console.log("SignalR reconnecting:", error);
    });

    connection.onreconnected((connectionId) => {
      console.log("SignalR reconnected:", connectionId);
    });

    connection.start()
      .then(() => {
        console.log("SignalR connection established successfully");
        console.log("Connection ID:", connection.connectionId);
        console.log("Connection state:", connection.state);
        console.log("Current user ID:", getCurrentUserId());
        console.log("Token present:", !!token);

        // Test the connection
        setTimeout(() => {
          connection.invoke("TestConnection").catch(err => {
            console.error("Test connection failed:", err);
          });
        }, 1000);
      })
      .catch(err => {
        console.error("SignalR connection failed:", err);
        console.error("Error details:", err.message);
        console.error("Token present:", !!token);
      });

    // Note: We don't auto-load videos on startup anymore since the new API
    // requires user interaction with the Google Photos picker
    // Videos will be loaded when user clicks "Load Videos from Google Photos"

    return () => {
      connection.stop();
    };
  }, [token]);



  const handleGoogleOAuth = async () => {
    try {
      const response = await fetch('/api/auth/google-oauth-url');
      const data = await response.json();
      window.location.href = data.authUrl;
    } catch (err) {
      console.error('Error getting OAuth URL:', err);
      showNotification('Error starting Google authentication', 'error');
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('jwt');
    setToken(null);
    setMediaItems([]);
  };

  const handleCompress = (mediaItem: MediaItem) => {
    setCompressionTarget(mediaItem);
    setCompressionModalOpen(true);
  };

  const handleBatchCompress = () => {
    if (selectedItems.size === 0) {
      showNotification('No items selected for compression', 'error');
      return;
    }

    setCompressionTarget('batch');
    setCompressionModalOpen(true);
  };

  const executeCompression = async (mediaItem: MediaItem, settings: typeof compressionSettings) => {
    try {
      const response = await fetch(`/api/videos/${mediaItem.id}/compress`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          quality: settings.quality,
          uploadToGooglePhotos: settings.uploadToGooglePhotos,
          overwriteOriginal: settings.overwriteOriginal,
          baseUrl: mediaItem.baseUrl,
          originalWidth: mediaItem.mediaMetadata?.width ? parseInt(mediaItem.mediaMetadata.width) : null,
          originalHeight: mediaItem.mediaMetadata?.height ? parseInt(mediaItem.mediaMetadata.height) : null
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to start compression: ${response.status}`);
      }

      const data = await response.json();
      console.log('Compression job queued:', data);

      // Immediately set the initial status
      setCompressionStatuses(prev => {
        const newMap = new Map(prev);
        newMap.set(mediaItem.id, {
          jobId: data.jobId,
          status: 'Queued',
          message: 'Queued for processing',
          progress: 0
        });
        return newMap;
      });

      return { success: true, jobId: data.jobId };
    } catch (err) {
      console.error('Error starting compression:', err);
      return { success: false, error: err };
    }
  };

  const handleConfirmCompression = async () => {
    setCompressionModalOpen(false);

    if (compressionTarget === 'batch') {
      const selectedMediaItems = mediaItems.filter(item => selectedItems.has(item.id));
      let successCount = 0;
      let errorCount = 0;

      showNotification(`Starting compression for ${selectedItems.size} items...`, 'info');

      for (const mediaItem of selectedMediaItems) {
        const result = await executeCompression(mediaItem, compressionSettings);
        if (result.success) {
          successCount++;
        } else {
          errorCount++;
        }
      }

      if (errorCount === 0) {
        showNotification(`Successfully started compression for ${successCount} items`, 'success');
      } else {
        showNotification(`Compression started for ${successCount} items, ${errorCount} failed`, 'error');
      }

      handleClearSelection();
    } else if (compressionTarget) {
      const result = await executeCompression(compressionTarget, compressionSettings);
      if (result.success) {
        showNotification(`Compression started for ${compressionTarget.filename}`, 'success');
      } else {
        showNotification('Error starting compression', 'error');
      }
    }

    setCompressionTarget(null);
  };

  const handleCancelCompression = () => {
    setCompressionModalOpen(false);
    setCompressionTarget(null);
  };

  const handleSelectFromGooglePhotos = async () => {
    if (!token) return;

    try {
      setVideosLoading(true);
      console.log('Creating picker session with token:', token.substring(0, 20) + '...');

      // Step 1: Create picker session
      const response = await fetch('/api/videos', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Response status:', response.status);
      const responseText = await response.text();
      console.log('Response text:', responseText);

      if (!response.ok) {
        const errorData = responseText ? JSON.parse(responseText) : {};

        // Handle re-authentication requirement
        if (response.status === 403 && errorData.requiresReauth) {
          showNotification('Google Photos access requires re-authentication. Redirecting...', 'info');
          setTimeout(() => {
            window.location.href = errorData.authUrl;
          }, 2000);
          return;
        }

        throw new Error(`Failed to create picker session: ${response.status} - ${responseText}`);
      }

      const sessionData = responseText ? JSON.parse(responseText) : {};
      console.log('Picker session created:', sessionData);

      if (!sessionData.pickerUri) {
        throw new Error('No picker URI received from server');
      }

      // Step 2: Open picker URI in new window/tab
      showNotification('Google Photos picker opened! Select your photos and videos, then click "Done". This page will automatically detect when you\'re finished.', 'info');
      const pickerWindow = window.open(sessionData.pickerUri, '_blank', 'width=1000,height=700');

      // Step 3: Poll for session completion
      let pollCount = 0;
      const maxPollAttempts = 150; // 5 minutes at 2-second intervals

      const pollSession = async () => {
        try {
          pollCount++;
          console.log(`Polling session status (attempt ${pollCount}/${maxPollAttempts})`);

          const statusResponse = await fetch(`/api/videos/session/${sessionData.sessionId}/status`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (!statusResponse.ok) {
            throw new Error(`Failed to check session status: ${statusResponse.status}`);
          }

          const statusData = await statusResponse.json();
          console.log('Session status:', statusData);

          if (statusData.mediaItemsSet) {
            // Step 4: Get selected videos
            console.log('Media items selected! Fetching videos...');
            const videosResponse = await fetch(`/api/videos/session/${sessionData.sessionId}/videos`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            if (!videosResponse.ok) {
              throw new Error(`Failed to fetch selected videos: ${videosResponse.status}`);
            }

            const mediaData = await videosResponse.json();
            console.log('Selected media response:', mediaData);
            console.log('Debug info:', mediaData.debug);
            console.log('Total selected items:', mediaData.totalSelected);
            console.log('Video count:', mediaData.videoCount);
            console.log('Photo count:', mediaData.photoCount);
            console.log('MIME types found:', mediaData.debug?.mimeTypes);

            setMediaItems(mediaData.mediaItems || []);

            const totalItems = mediaData.totalSelected || 0;
            const videoCount = mediaData.videoCount || 0;
            const photoCount = mediaData.photoCount || 0;

            if (totalItems > 0) {
              let message = `Successfully loaded ${totalItems} items from Google Photos`;
              if (videoCount > 0 && photoCount > 0) {
                message += ` (${videoCount} videos, ${photoCount} photos)`;
              } else if (videoCount > 0) {
                message += ` (${videoCount} videos)`;
              } else if (photoCount > 0) {
                message += ` (${photoCount} photos)`;
              }
              showNotification(message, 'success');
            } else {
              showNotification('No items were selected', 'info');
            }

            // Step 5: Clean up session
            try {
              await fetch(`/api/videos/session/${sessionData.sessionId}`, {
                method: 'DELETE',
                headers: {
                  'Authorization': `Bearer ${token}`
                }
              });
              console.log('Session cleaned up successfully');
            } catch (cleanupErr) {
              console.warn('Failed to clean up session:', cleanupErr);
            }

            if (pickerWindow && !pickerWindow.closed) {
              pickerWindow.close();
            }
            setVideosLoading(false);
          } else {
            // Continue polling if we haven't exceeded max attempts and window is still open
            if (pollCount < maxPollAttempts && pickerWindow && !pickerWindow.closed) {
              // Use Google's recommended polling interval (default to 2 seconds if not provided)
              const pollInterval = statusData.pollingConfig?.pollInterval ?
                parseInt(statusData.pollingConfig.pollInterval.replace('s', '')) * 1000 : 2000;

              console.log(`Continuing to poll in ${pollInterval}ms...`);
              setTimeout(pollSession, pollInterval);
            } else if (pollCount >= maxPollAttempts) {
              // Timeout reached
              showNotification('Video selection timed out. Please try again.', 'error');
              setVideosLoading(false);
            } else {
              // User closed picker window without selecting
              showNotification('Video selection cancelled', 'info');
              setVideosLoading(false);
            }
          }
        } catch (pollErr) {
          console.error('Error polling session:', pollErr);

          // Only show error if we've made several attempts (avoid showing errors too early)
          if (pollCount > 3) {
            showNotification('Error checking selection status', 'error');
            setVideosLoading(false);
          } else {
            // Retry after a longer delay for early errors
            if (pickerWindow && !pickerWindow.closed) {
              setTimeout(pollSession, 5000);
            } else {
              setVideosLoading(false);
            }
          }
        }
      };

      // Start polling after giving user time to interact with picker (10 seconds)
      console.log('Starting polling in 10 seconds...');
      setTimeout(pollSession, 10000);

    } catch (err) {
      console.error('Error loading videos:', err);
      showNotification(`Error loading videos: ${err instanceof Error ? err.message : 'Unknown error'}`, 'error');
      setVideosLoading(false);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <Router>
        <AppBar
          position="static"
          elevation={0}
          sx={{
            backgroundColor: 'background.paper',
            borderBottom: 1,
            borderColor: 'divider',
            color: 'text.primary'
          }}
        >
          <Toolbar sx={{ py: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
              <Box
                component="img"
                src="/gallery_tuner.png"
                alt="Gallery Tuner"
                sx={{
                  width: 32,
                  height: 32,
                  mr: 2,
                  objectFit: 'contain' // Preserves aspect ratio without cropping
                }}
              />
              <Typography
                variant="h6"
                component="div"
                sx={{
                  fontWeight: 500,
                  color: 'text.primary',
                  letterSpacing: '0.25px'
                }}
              >
                Gallery Tuner
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ThemeSelector />
              {token ? (
                <>
                  <IconButton
                    onClick={handleSelectFromGooglePhotos}
                    sx={{
                      color: 'text.secondary',
                      '&:hover': {
                        backgroundColor: 'action.hover',
                        color: 'primary.main'
                      }
                    }}
                  >
                    <Refresh />
                  </IconButton>
                  <Button
                    onClick={handleLogout}
                    startIcon={<Logout />}
                    sx={{
                      color: 'text.secondary',
                      textTransform: 'none',
                      fontWeight: 500,
                      '&:hover': {
                        backgroundColor: 'action.hover',
                        color: 'error.main'
                      },
                      borderRadius: 1
                    }}
                  >
                    Sign out
                  </Button>
                </>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleGoogleOAuth}
                  startIcon={<AccountCircle />}
                  sx={{
                    backgroundColor: 'primary.main',
                    color: 'primary.contrastText',
                    textTransform: 'none',
                    fontWeight: 500,
                    borderRadius: 1,
                    px: 3,
                    boxShadow: 'none',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                      boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)'
                    }
                  }}
                >
                  Sign in
                </Button>
              )}
            </Box>
          </Toolbar>
        </AppBar>
        <Box sx={{
          minHeight: '100vh',
          backgroundColor: 'background.default',
          pb: 4
        }}>
          <Container maxWidth="lg" sx={{ pt: 6 }}>
            <Snackbar
              open={!!notification}
              autoHideDuration={6000}
              onClose={() => setNotification(null)}
              anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
              <Alert
                onClose={() => setNotification(null)}
                severity={notificationType}
                variant="filled"
                sx={{ width: '100%' }}
              >
                {notification}
              </Alert>
            </Snackbar>
            <Routes>
            <Route path="/" element={
              <Fade in timeout={800}>
                <Box>

                  {token && (
                    <Paper
                      elevation={0}
                      sx={{
                        p: 3,
                        mb: 4,
                        backgroundColor: 'grey.50',
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 2
                      }}
                    >
                      <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                        <Button
                          variant="contained"
                          size="large"
                          startIcon={<VideoLibrary />}
                          onClick={handleSelectFromGooglePhotos}
                          disabled={videosLoading}
                          sx={{
                            backgroundColor: 'primary.main',
                            color: 'primary.contrastText',
                            textTransform: 'none',
                            fontWeight: 500,
                            boxShadow: 'none',
                            '&:hover': {
                              backgroundColor: 'primary.dark',
                              boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)'
                            },
                            '&:disabled': {
                              backgroundColor: 'action.disabledBackground',
                              color: 'action.disabled'
                            },
                            borderRadius: 1,
                            px: 3,
                            py: 1.5
                          }}
                        >
                          {videosLoading ? 'Waiting for media selection...' : 'Load Media from Google Photos'}
                        </Button>
                        <Subscribe />
                        <Chip
                          label={`${mediaItems.length} items loaded`}
                          variant="outlined"
                          sx={{
                            ml: 'auto',
                            borderColor: 'success.main',
                            color: 'success.dark',
                            backgroundColor: 'rgba(52, 168, 83, 0.1)',
                            fontWeight: 500,
                            '& .MuiChip-label': {
                              color: 'success.dark'
                            }
                          }}
                        />
                      </Box>
                    </Paper>
                  )}
                  {token ? (
                    mediaItems.length > 0 ? (
                      <>
                        {/* Selection Controls */}
                        {selectedItems.size > 0 && (
                          <Paper
                            elevation={0}
                            sx={{
                              p: 2,
                              mb: 3,
                              backgroundColor: (theme) =>
                                theme.palette.mode === 'dark'
                                  ? 'primary.light'
                                  : 'rgba(26, 115, 232, 0.08)',
                              border: 1,
                              borderColor: 'primary.main',
                              borderRadius: 2,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              flexWrap: 'wrap',
                              gap: 2
                            }}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Chip
                                label={`${selectedItems.size} item${selectedItems.size > 1 ? 's' : ''} selected`}
                                sx={{
                                  backgroundColor: 'primary.main',
                                  color: 'primary.contrastText',
                                  fontWeight: 500
                                }}
                              />
                              <Button
                                variant="outlined"
                                size="small"
                                startIcon={<SelectAll />}
                                onClick={handleSelectAll}
                                sx={{
                                  borderColor: 'primary.main',
                                  color: 'primary.main',
                                  textTransform: 'none',
                                  '&:hover': {
                                    backgroundColor: 'action.hover',
                                    borderColor: 'primary.dark'
                                  }
                                }}
                              >
                                Select All
                              </Button>
                              <Button
                                variant="outlined"
                                size="small"
                                startIcon={<Clear />}
                                onClick={handleClearSelection}
                                sx={{
                                  borderColor: 'text.secondary',
                                  color: 'text.secondary',
                                  textTransform: 'none',
                                  '&:hover': {
                                    backgroundColor: 'action.hover',
                                    borderColor: 'text.primary'
                                  }
                                }}
                              >
                                Clear Selection
                              </Button>
                            </Box>
                            <Button
                              variant="contained"
                              startIcon={<Compress />}
                              onClick={handleBatchCompress}
                              sx={{
                                backgroundColor: 'primary.main',
                                color: 'primary.contrastText',
                                textTransform: 'none',
                                fontWeight: 500,
                                boxShadow: 'none',
                                '&:hover': {
                                  backgroundColor: 'primary.dark',
                                  boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)'
                                },
                                borderRadius: 1,
                                px: 3
                              }}
                            >
                              Compress Selected ({selectedItems.size})
                            </Button>
                          </Paper>
                        )}

                        {/* Instructions */}
                        {selectedItems.size === 0 && (
                          <Box sx={{ mb: 3, textAlign: 'center' }}>
                            <Typography variant="body2" sx={{ color: 'text.secondary', fontSize: '14px' }}>
                              Click to select items • Shift+click to select range • Ctrl/Cmd+click to toggle selection
                            </Typography>
                          </Box>
                        )}

                        <Box
                          sx={{
                            display: 'grid',
                            gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
                            gap: 0,
                            width: '100%',
                            userSelect: 'none',
                            WebkitUserSelect: 'none',
                            MozUserSelect: 'none',
                            msUserSelect: 'none'
                          }}
                        >
                        {mediaItems.map((mediaItem, index) => {
                          const isSelected = selectedItems.has(mediaItem.id);
                          return (
                            <Box
                              key={mediaItem.id || index}
                              onClick={(e) => handleItemClick(mediaItem, index, e)}
                              sx={{
                                position: 'relative',
                                aspectRatio: '1',
                                overflow: 'hidden',
                                cursor: 'pointer',
                                border: isSelected ? '3px solid' : '3px solid transparent',
                                borderColor: isSelected ? 'primary.main' : 'transparent',
                                transition: 'border-color 0.2s ease',
                                userSelect: 'none',
                                WebkitUserSelect: 'none',
                                MozUserSelect: 'none',
                                msUserSelect: 'none',
                                '&:hover .media-overlay': {
                                  opacity: 1
                                },
                                '&:hover .media-image': {
                                  transform: 'scale(1.05)'
                                },
                                '&:hover .selection-indicator': {
                                  opacity: 1
                                }
                              }}
                            >
                            {/* Media Image */}
                            {mediaItem.id && token ? (
                              <img
                                className="media-image"
                                src={`/api/videos/${mediaItem.id}/preview?width=400&height=400&crop=true&token=${encodeURIComponent(token)}&baseUrl=${encodeURIComponent(mediaItem.baseUrl)}`}
                                alt={mediaItem.filename}
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'cover',
                                  transition: 'transform 0.2s ease, opacity 0.3s ease',
                                  display: 'block',
                                  opacity: 0
                                }}
                                onLoad={(e) => {
                                  // Fade in the image once it's loaded
                                  const target = e.currentTarget;
                                  target.style.opacity = '1';
                                }}
                                onError={(e) => {
                                  // Fallback if image fails to load
                                  const target = e.currentTarget;
                                  target.style.display = 'none';
                                  const parent = target.parentElement!;
                                  const fallbackDiv = document.createElement('div');
                                  fallbackDiv.className = 'media-image';
                                  fallbackDiv.style.cssText = `
                                    width: 100%;
                                    height: 100%;
                                    display: flex;
                                    flex-direction: column;
                                    align-items: center;
                                    justify-content: center;
                                    background-color: #f8f9fa;
                                    color: #5f6368;
                                    transition: transform 0.2s ease, opacity 0.3s ease;
                                    opacity: 0;
                                  `;
                                  fallbackDiv.innerHTML = `
                                    <span style="font-size: 48px;">${mediaItem.mimeType?.startsWith('video/') ? '🎥' : '📷'}</span>
                                    <span style="margin-top: 8px; font-size: 12px;">Preview unavailable</span>
                                  `;
                                  parent.appendChild(fallbackDiv);
                                  // Fade in the fallback
                                  setTimeout(() => {
                                    fallbackDiv.style.opacity = '1';
                                  }, 10);
                                }}
                              />
                            ) : (
                              <Fade in={true} timeout={300}>
                                <Box
                                  className="media-image"
                                  sx={{
                                    width: '100%',
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: 'grey.100',
                                    color: 'text.secondary',
                                    transition: 'transform 0.2s ease'
                                  }}
                                >
                                  <Box sx={{ fontSize: '48px', mb: 1 }}>
                                    {mediaItem.mimeType?.startsWith('video/') ? '🎥' : '📷'}
                                  </Box>
                                  <Typography variant="body2" sx={{ fontSize: '12px' }}>
                                    Preview unavailable
                                  </Typography>
                                </Box>
                              </Fade>
                            )}

                            {/* Video Duration Badge */}
                            {mediaItem.mimeType?.startsWith('video/') && (
                              <Box
                                sx={{
                                  position: 'absolute',
                                  bottom: 8,
                                  right: 8,
                                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                  color: 'white',
                                  padding: '2px 6px',
                                  borderRadius: '4px',
                                  fontSize: '12px',
                                  fontWeight: 500,
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 0.5
                                }}
                              >
                                <PlayArrow sx={{ fontSize: 14 }} />
                                Video
                              </Box>
                            )}

                            {/* Compression Status Indicator */}
                            <CompressionStatusIndicator
                              mediaItemId={mediaItem.id}
                              status={compressionStatuses.get(mediaItem.id)}
                            />

                            {/* Selection Indicator */}
                            <Box
                              className="selection-indicator"
                              sx={{
                                position: 'absolute',
                                top: 8,
                                right: 8,
                                width: 24,
                                height: 24,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                zIndex: 2,
                                opacity: isSelected ? 1 : 0,
                                transition: 'opacity 0.2s ease'
                              }}
                            >
                              {isSelected ? (
                                <CheckCircle
                                  sx={{
                                    fontSize: 24,
                                    color: 'primary.main',
                                    backgroundColor: 'background.paper',
                                    borderRadius: '50%'
                                  }}
                                />
                              ) : (
                                <CheckCircleOutline
                                  sx={{
                                    fontSize: 24,
                                    color: 'white',
                                    filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.5))'
                                  }}
                                />
                              )}
                            </Box>

                            {/* Hover Overlay */}
                            <Box
                              className="media-overlay"
                              sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                background: 'linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0) 30%, rgba(0,0,0,0) 70%, rgba(0,0,0,0.5) 100%)',
                                opacity: isSelected ? 0.7 : 0,
                                transition: 'opacity 0.2s ease',
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'space-between',
                                padding: 1
                              }}
                            >
                              {/* Top Info */}
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                <Box sx={{ display: 'flex', gap: 0.5 }}>
                                  <Chip
                                    label={mediaItem.mimeType?.startsWith('video/') ? 'Video' : 'Photo'}
                                    size="small"
                                    sx={{
                                      backgroundColor: 'background.paper',
                                      color: mediaItem.mimeType?.startsWith('video/') ? 'primary.main' : 'success.main',
                                      fontWeight: 500,
                                      fontSize: '10px',
                                      height: 20,
                                      border: 1,
                                      borderColor: 'divider'
                                    }}
                                  />
                                  {mediaItem.mediaMetadata?.width && mediaItem.mediaMetadata?.height && (
                                    <Chip
                                      label={`${mediaItem.mediaMetadata.width}×${mediaItem.mediaMetadata.height}`}
                                      size="small"
                                      sx={{
                                        backgroundColor: 'background.paper',
                                        color: 'text.secondary',
                                        fontSize: '10px',
                                        height: 20,
                                        border: 1,
                                        borderColor: 'divider'
                                      }}
                                    />
                                  )}
                                </Box>
                              </Box>

                              {/* Bottom Actions */}
                              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: 'white',
                                    fontWeight: 500,
                                    fontSize: '12px',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                    textShadow: '0 1px 2px rgba(0,0,0,0.5)'
                                  }}
                                >
                                  {mediaItem.filename}
                                </Typography>
                                <Button
                                  variant="contained"
                                  size="small"
                                  startIcon={<Compress />}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleCompress(mediaItem);
                                  }}
                                  sx={{
                                    backgroundColor: 'primary.main',
                                    color: 'primary.contrastText',
                                    textTransform: 'none',
                                    fontWeight: 500,
                                    fontSize: '12px',
                                    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                                    '&:hover': {
                                      backgroundColor: 'primary.dark',
                                      boxShadow: '0 4px 8px rgba(0,0,0,0.3)'
                                    },
                                    borderRadius: 1,
                                    py: 0.5,
                                    px: 1
                                  }}
                                >
                                  Compress
                                </Button>
                              </Box>
                            </Box>
                          </Box>
                          );
                        })}
                      </Box>
                      </>
                    ) : (
                      <Paper
                        elevation={0}
                        sx={{
                          p: 6,
                          textAlign: 'center',
                          backgroundColor: 'grey.50',
                          border: 1,
                          borderColor: 'divider',
                          borderRadius: 2
                        }}
                      >
                        <VideoLibrary sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h5" gutterBottom sx={{ color: 'text.primary', fontWeight: 400 }}>
                          No media found
                        </Typography>
                        <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                          Click "Load Media from Google Photos" to get started
                        </Typography>
                      </Paper>
                    )
                  ) : (
                    <Paper
                      elevation={0}
                      sx={{
                        p: 8,
                        textAlign: 'center',
                        backgroundColor: 'grey.50',
                        border: 1,
                        borderColor: 'divider',
                        borderRadius: 2
                      }}
                    >
                      <Box sx={{ mb: 3 }}>
                        <Box sx={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 80,
                          height: 80,
                          borderRadius: '50%',
                          backgroundColor: 'grey.100',
                          mb: 2
                        }}>
                          <AccountCircle sx={{ fontSize: 48, color: 'text.secondary' }} />
                        </Box>
                      </Box>
                      <Typography variant="h4" gutterBottom sx={{ fontWeight: 400, color: 'text.primary' }}>
                        Welcome to Gallery Tuner
                      </Typography>
                      <Typography variant="body1" sx={{ color: 'text.secondary', mb: 4, maxWidth: 400, mx: 'auto', lineHeight: 1.6 }}>
                        Sign in with your Google account to access and adjust your media from Google Photos
                      </Typography>
                      <Button
                        variant="contained"
                        size="large"
                        startIcon={<AccountCircle />}
                        onClick={handleGoogleOAuth}
                        sx={{
                          backgroundColor: 'primary.main',
                          color: 'primary.contrastText',
                          textTransform: 'none',
                          fontWeight: 500,
                          boxShadow: 'none',
                          '&:hover': {
                            backgroundColor: 'primary.dark',
                            boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)'
                          },
                          borderRadius: 1,
                          px: 4,
                          py: 1.5
                        }}
                      >
                        Sign in with Google
                      </Button>
                    </Paper>
                  )}
                </Box>
              </Fade>
            } />
            <Route path="/auth/callback" element={<AuthCallback />} />
            <Route path="/success" element={
              <Paper
                elevation={0}
                sx={{
                  p: 6,
                  textAlign: 'center',
                  backgroundColor: 'background.paper',
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 2
                }}
              >
                <Typography variant="h4" sx={{ color: 'success.main', fontWeight: 400 }} gutterBottom>
                  Thanks for subscribing!
                </Typography>
                <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                  You now have access to premium compression features.
                </Typography>
              </Paper>
            } />
            <Route path="/cancel" element={
              <Paper
                elevation={0}
                sx={{
                  p: 6,
                  textAlign: 'center',
                  backgroundColor: 'background.paper',
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 2
                }}
              >
                <Typography variant="h4" sx={{ color: 'error.main', fontWeight: 400 }} gutterBottom>
                  Subscription canceled
                </Typography>
                <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                  You can subscribe again anytime to access premium features.
                </Typography>
              </Paper>
            } />
          </Routes>
          </Container>
        </Box>

        {/* Compression Settings Modal */}
        <Dialog
          open={compressionModalOpen}
          onClose={handleCancelCompression}
          maxWidth="sm"
          fullWidth
          sx={{
            '& .MuiDialog-paper': {
              borderRadius: 3,
              p: 2
            }
          }}
        >
          <DialogTitle sx={{ pb: 1 }}>
            <Typography variant="h5" component="div" sx={{ fontWeight: 500 }}>
              Compression Settings
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              {compressionTarget === 'batch'
                ? `Configure settings for ${selectedItems.size} selected items`
                : `Configure settings for ${compressionTarget?.filename || 'this video'}`
              }
            </Typography>
          </DialogTitle>

          <DialogContent sx={{ pt: 2 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
                Quality Settings
              </Typography>
              <FormControl fullWidth>
                <InputLabel>Compression Quality</InputLabel>
                <Select
                  value={compressionSettings.quality}
                  label="Compression Quality"
                  onChange={(e) => setCompressionSettings(prev => ({ ...prev, quality: e.target.value }))}
                >
                  <MenuItem value="high">
                    <Box>
                      <Typography variant="body1">High Quality</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Better quality, larger file size
                      </Typography>
                    </Box>
                  </MenuItem>
                  <MenuItem value="medium">
                    <Box>
                      <Typography variant="body1">Medium Quality</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Balanced quality and size
                      </Typography>
                    </Box>
                  </MenuItem>
                  <MenuItem value="low">
                    <Box>
                      <Typography variant="body1">Low Quality</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Smaller file size, lower quality
                      </Typography>
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
                Options
              </Typography>

              {/* Upload to Google Photos Option */}
              <FormControlLabel
                control={
                  <Checkbox
                    checked={compressionSettings.uploadToGooglePhotos}
                    onChange={(e) => setCompressionSettings(prev => ({
                      ...prev,
                      uploadToGooglePhotos: e.target.checked,
                      // If unchecking upload, also uncheck overwrite
                      overwriteOriginal: e.target.checked ? prev.overwriteOriginal : false
                    }))}
                  />
                }
                label={
                  <Box>
                    <Typography variant="body1">Upload compressed video to Google Photos</Typography>
                    <Typography variant="caption" color="text.secondary">
                      Save the compressed video back to your Google Photos library
                    </Typography>
                  </Box>
                }
                sx={{ mb: 2 }}
              />

              {/* Replace Original Option */}
              <FormControlLabel
                control={
                  <Checkbox
                    checked={compressionSettings.overwriteOriginal}
                    disabled={!compressionSettings.uploadToGooglePhotos}
                    onChange={(e) => setCompressionSettings(prev => ({ ...prev, overwriteOriginal: e.target.checked }))}
                  />
                }
                label={
                  <Box>
                    <Typography
                      variant="body1"
                      color={compressionSettings.uploadToGooglePhotos ? 'text.primary' : 'text.disabled'}
                    >
                      Replace original videos
                    </Typography>
                    <Typography
                      variant="caption"
                      color={compressionSettings.uploadToGooglePhotos ? 'text.secondary' : 'text.disabled'}
                    >
                      {compressionSettings.uploadToGooglePhotos
                        ? 'Delete the original videos after uploading compressed versions'
                        : 'Only available when uploading to Google Photos'
                      }
                    </Typography>
                  </Box>
                }
              />
            </Box>
          </DialogContent>

          <DialogActions sx={{ px: 3, pb: 2 }}>
            <Button onClick={handleCancelCompression} color="inherit">
              Cancel
            </Button>
            <Button
              onClick={handleConfirmCompression}
              variant="contained"
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 500
              }}
            >
              Start Compression
            </Button>
          </DialogActions>
        </Dialog>

      </Router>
    </ThemeProvider>
  );
};

// Main App component with theme provider
const App: React.FC = () => {
  return (
    <CustomThemeProvider>
      <AppContent />
    </CustomThemeProvider>
  );
};

export default App;
